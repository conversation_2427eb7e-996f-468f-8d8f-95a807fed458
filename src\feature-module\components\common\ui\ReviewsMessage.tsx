import { <PERSON>, Card<PERSON>ody, CardHeader, Avatar } from '@heroui/react';
import { ReactNode } from 'react';

import { FaRegStar, FaStar } from 'react-icons/fa6';
import Reply from './Reply';

interface ReusableCardProps {
  title?: string;
  subtitle?: string;
  mainContent?: ReactNode;
  footerContent?: ReactNode;
  isFooterDevider?: boolean;
  isHeaderDevider?: boolean;
  className?: string;
}

const ReviewsMessage = ({
  //   title,
  //   subtitle,
  //   mainContent,
  //   footerContent,
  //   isFooterDevider,
  //   isHeaderDevider,
  className = '',
}: ReusableCardProps) => {
  return (
    <div>
      <Card className={`border border-primary shadow-sm ${className}`}>
        <CardHeader className="flex justify-between items-center">
          <div className="flex ">
            <Avatar
              name="JANE DOE"
              alt="Jane Doe"
              size="sm"
              color="secondary"
            />
            <div className="ml-2">
              <div className="font-medium text-xs"><PERSON></div>
              <div className="text-[11px] font-thin text-primary -mt-[2px]">
                Product Designer
              </div>
            </div>
          </div>

          <div className="flex flex-col items-end">
            <div className="flex gap-1">
              <FaStar className="text-sm fill-yellow-400" />
              <FaStar className="text-sm fill-yellow-400" />
              <FaStar className="text-sm fill-yellow-400" />
              <FaRegStar className="text-sm text-gray-400" />
            </div>
            <p className="text-[11px] mt-1 text-end text-gray-700">
              Jul 31, 2025
            </p>
          </div>
        </CardHeader>
        <CardBody className="-mt-3">
          <p className="text-xs text-gray-700">
            Amazing pet sitter! My dogs absolutely loved John. He sent regular
            updates and photos throughout the day. Highly recommend for anyone
            needing reliable pet care.
          </p>
          <Reply />
        </CardBody>
      </Card>
    </div>
  );
};

export default ReviewsMessage;
