import React, { useState, useEffect } from 'react';
import { Button } from '@heroui/react';
import { 
  getImageUrlFromName, 
  getImageUrlWithFallback, 
  getReliableImageUrl,
  debugS3Configuration 
} from '../../frontend/Customer/aws/s3FileUpload';

interface ImageUrlTestProps {
  testImageName?: string;
  folderName?: string;
}

const ImageUrlTest: React.FC<ImageUrlTestProps> = ({ 
  testImageName = 'test-image.jpg',
  folderName = 'review-images'
}) => {
  const [urls, setUrls] = useState<{
    primary: string;
    fallback: string;
    reliable: string;
  }>({
    primary: '',
    fallback: '',
    reliable: ''
  });

  const [loading, setLoading] = useState(false);

  const generateUrls = async () => {
    setLoading(true);
    try {
      const primaryUrl = getImageUrlFromName(testImageName, folderName);
      const fallbackUrl = getImageUrlWithFallback(testImageName, folderName);
      const reliableUrl = await getReliableImageUrl(testImageName, folderName);

      setUrls({
        primary: primaryUrl,
        fallback: fallbackUrl,
        reliable: reliableUrl
      });

      console.log('Image URL Test Results:', {
        testImageName,
        folderName,
        primaryUrl,
        fallbackUrl,
        reliableUrl
      });
    } catch (error) {
      console.error('Error generating URLs:', error);
    } finally {
      setLoading(false);
    }
  };

  const runDebug = () => {
    debugS3Configuration();
  };

  useEffect(() => {
    generateUrls();
  }, [testImageName, folderName]);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Image URL Configuration Test</h2>
      
      <div className="mb-4 space-y-2">
        <div>
          <strong>Test Image Name:</strong> {testImageName}
        </div>
        <div>
          <strong>Folder Name:</strong> {folderName}
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <Button 
          onClick={generateUrls} 
          disabled={loading}
          color="primary"
        >
          {loading ? 'Generating URLs...' : 'Regenerate URLs'}
        </Button>
        
        <Button 
          onClick={runDebug} 
          color="secondary"
          variant="bordered"
        >
          Run S3 Configuration Debug
        </Button>
      </div>

      <div className="space-y-4">
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold text-lg mb-2">Primary URL (getImageUrlFromName)</h3>
          <div className="bg-gray-100 p-2 rounded text-sm font-mono break-all">
            {urls.primary || 'Not generated'}
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Uses CDN if available, falls back to S3 direct URL
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-semibold text-lg mb-2">Fallback URL (getImageUrlWithFallback)</h3>
          <div className="bg-gray-100 p-2 rounded text-sm font-mono break-all">
            {urls.fallback || 'Not generated'}
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Prioritizes CDN, falls back to S3 direct URL
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-semibold text-lg mb-2">Reliable URL (getReliableImageUrl)</h3>
          <div className="bg-gray-100 p-2 rounded text-sm font-mono break-all">
            {urls.reliable || 'Not generated'}
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Async function with enhanced reliability checks
          </div>
        </div>
      </div>

      {/* URL Comparison */}
      <div className="mt-6 border rounded-lg p-4">
        <h3 className="font-semibold text-lg mb-2">URL Analysis</h3>
        <div className="space-y-2 text-sm">
          <div>
            <strong>Using CDN:</strong> {
              urls.primary.includes('cdn.staging.gigmosaic.ca') ? 
              <span className="text-green-600">Yes</span> : 
              <span className="text-orange-600">No</span>
            }
          </div>
          <div>
            <strong>Using S3 Direct:</strong> {
              urls.primary.includes('amazonaws.com') ? 
              <span className="text-blue-600">Yes</span> : 
              <span className="text-gray-600">No</span>
            }
          </div>
          <div>
            <strong>URLs Match:</strong> {
              urls.primary === urls.fallback && urls.fallback === urls.reliable ? 
              <span className="text-green-600">All match</span> : 
              <span className="text-orange-600">Different URLs</span>
            }
          </div>
        </div>
      </div>

      {/* Image Preview */}
      {urls.primary && (
        <div className="mt-6 border rounded-lg p-4">
          <h3 className="font-semibold text-lg mb-2">Image Preview</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Primary URL:</h4>
              <img 
                src={urls.primary} 
                alt="Primary URL test"
                className="w-32 h-32 object-cover rounded border"
                onLoad={() => console.log('Primary URL loaded successfully')}
                onError={(e) => {
                  console.error('Primary URL failed to load');
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/128x128?text=Failed&bg=ff0000&color=ffffff';
                }}
              />
            </div>
            
            {urls.fallback !== urls.primary && (
              <div>
                <h4 className="font-medium mb-2">Fallback URL:</h4>
                <img 
                  src={urls.fallback} 
                  alt="Fallback URL test"
                  className="w-32 h-32 object-cover rounded border"
                  onLoad={() => console.log('Fallback URL loaded successfully')}
                  onError={(e) => {
                    console.error('Fallback URL failed to load');
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/128x128?text=Failed&bg=ff0000&color=ffffff';
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUrlTest;
