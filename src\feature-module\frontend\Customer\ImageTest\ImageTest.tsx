import React, { useState } from 'react';
import { Input, <PERSON><PERSON>, Card, CardBody, CardHeader } from '@heroui/react';
import ImageUrlTest from '../../../components/ImageUrlTest/ImageUrlTest';

const ImageTest: React.FC = () => {
  const [testImageName, setTestImageName] = useState('test-image.jpg');
  const [folderName, setFolderName] = useState('review-images');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            S3 & CDN Image URL Configuration Test
          </h1>
          
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold">Test Parameters</h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Test Image Name"
                  placeholder="Enter image name (e.g., test-image.jpg)"
                  value={testImageName}
                  onChange={(e) => setTestImageName(e.target.value)}
                />
                <Input
                  label="Folder Name"
                  placeholder="Enter folder name (e.g., review-images)"
                  value={folderName}
                  onChange={(e) => setFolderName(e.target.value)}
                />
              </div>
            </CardBody>
          </Card>

          <ImageUrlTest 
            testImageName={testImageName}
            folderName={folderName}
          />

          <Card className="mt-8">
            <CardHeader>
              <h2 className="text-xl font-semibold">Configuration Information</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4 text-sm">
                <div>
                  <h3 className="font-semibold mb-2">Environment Variables:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li><code>VITE_S3_BUCKET_NAME</code>: S3 bucket name</li>
                    <li><code>VITE_S3_BUCKET_REGION</code>: S3 bucket region</li>
                    <li><code>VITE_S3_CDN_URL</code>: CDN base URL</li>
                    <li><code>VITE_S3_ACCESS_KEY</code>: S3 access key</li>
                    <li><code>VITE_S3_SECRET_KEY</code>: S3 secret key</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">URL Priority:</h3>
                  <ol className="list-decimal list-inside space-y-1 text-gray-600">
                    <li>CDN URL (if configured and available)</li>
                    <li>S3 Direct URL (fallback)</li>
                    <li>Placeholder image (final fallback)</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Image Storage:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li>Images are uploaded to S3 with unique names</li>
                    <li>Only image names (not full URLs) are stored in database</li>
                    <li>Full URLs are constructed dynamically for display</li>
                    <li>CDN provides faster global access when available</li>
                  </ul>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ImageTest;
